<!--
  Copyright 2015 Google Inc. All Rights Reserved.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<html>
<head>
    <title>Hello World Guestbook</title>
    <link rel="stylesheet" type="text/css" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.5/css/bootstrap.min.css"/>
    <meta charset="utf-8"></meta>
    <meta name="viewport" content="width=device-width, initial-scale=1"></meta>
    <style>
      body { padding-top: 80px; }
      form span { font-weight: bold; padding-right: 1em; display: block; }
      form input[type="submit"] { display: block; margin-top: 1em; }
      .greeting { padding-bottom: 0.5em; }
      .messages { border-top: 1px solid black }
      .message { display: block; }
      .message span { padding-top: 0.5em; }
      .message .username { font-weight: bold; width: 100px; float: left;}
    </style>
</head>
<body>
<nav class="navbar navbar-inverse navbar-fixed-top">
    <div class="container">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar" aria-expanded="false" aria-controls="navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <a class="navbar-brand" href="#">Guestbook</a>
        </div>
        <div id="navbar" class="collapse navbar-collapse">
            <ul class="nav navbar-nav">
                <li class="active"><a href="#">Home</a></li>
            </ul>
        </div><!--/.nav-collapse -->
    </div>
</nav>

<div class="main container">
    <div class="input">
        <form action="/greet" method="post">
            <span>Your name:</span><input type="text" name="name" th:value="${name}"/>
            <span>Message:</span><input type="text" name="message"/>
            <input type="submit" value="Greet!"/>
        </form>
    </div>

    <div class="messages">
        <div th:each="message: ${messages}" class="message">
            <span th:text="${message.username}" class="username">Username</span>
            <span th:text="${message.message}" class="message">Message</span>
        </div>
    </div>

</div>

<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.5/js/bootstrap.min.js"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.3/jquery.min.js"></script>

</body>
</html>
