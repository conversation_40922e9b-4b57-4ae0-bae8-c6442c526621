syntax = "proto3";

package com.example.guestbook;

option java_multiple_files = true;

message AllRequest {
}

message DeleteRequest {
    int64 id = 1;
}

message FindOneRequest {
    int64 id = 1;
}

message GuestbookEntry {
    int64 id = 1;
    string username = 2;
    string message = 3;
}

message DeleteResponse {
}

message AddRequest {
    string username = 1;
    string message = 2;
}

message AddResponse {
    int64 id = 1;
}

service GuestbookService {
    rpc all(AllRequest) returns (stream GuestbookEntry);
    rpc findOne(FindOneRequest) returns (GuestbookEntry);
    rpc delete(DeleteRequest) returns (DeleteResponse);
    rpc add(AddRequest) returns (AddResponse);
}
