apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    run: echo-client
  name: echo-client
spec:
  replicas: 2
  selector:
    matchLabels:
      run: echo-client
  template:
    metadata:
      labels:
        run: echo-client
    spec:
      containers:
      - name: echo-client
        image: saturnism/echo-client-lb-api
        env:
        - name: ECHO_SERVICE_TARGET
          value: kubernetes:///default/echo-server/8080
