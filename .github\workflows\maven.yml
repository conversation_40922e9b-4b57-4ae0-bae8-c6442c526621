name: Java CI

on:
  pull_request:
    branches:
    - master
  push:
    branches:
    - master

jobs:
  build:
    runs-on: ubuntu-18.04
    strategy:
      matrix:
        java_version: [11, 12]

    steps:
    - uses: actions/checkout@v1
    - name: Set up JDK
      uses: actions/setup-java@v1
      with:
        java-version: ${{ matrix.java_version }}
    - name: Build with <PERSON><PERSON>
      run: ./mvnw -B package verify
