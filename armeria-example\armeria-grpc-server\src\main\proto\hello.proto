syntax = "proto3";

package example.grpc.hello;
option java_package = "com.example.grpc.armeria";

service HelloService {
  rpc Hello (HelloRequest) returns (HelloReply) {}
  rpc <PERSON><PERSON><PERSON>ello (HelloRequest) returns (HelloReply) {}
  rpc <PERSON><PERSON><PERSON><PERSON> (HelloRequest) returns (HelloReply) {}
  rpc LotsOfReplies (HelloRequest) returns (stream HelloReply) {}
  rpc LotsOfGreetings (stream HelloRequest) returns (HelloReply) {}
  rpc <PERSON>id<PERSON><PERSON><PERSON> (stream HelloRequest) returns (stream HelloReply) {}
}

message HelloRequest {
  string name = 1;
}

message HelloReply {
  string message = 1;
}
