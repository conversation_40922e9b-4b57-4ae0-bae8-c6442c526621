/*
 * Copyright 2016 Google, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// 1. syntax, package, option
syntax = "proto3";

package com.example.grpc;

option java_multiple_files = true;

enum Sentiment {
    HAPPY = 0;
    SLEEPY = 1;
    ANGRY = 2;
}

message HelloRequest {
    string name = 1;
    int32 age = 2;
    repeated string hobbies = 3;
    map<string, string> bagOfTricks = 4;
    Sentiment sentiment = 5;
}

message HelloResponse {
    string greeting = 1;
}

// 4. service, unary request/response
service GreetingService {
    rpc greeting(HelloRequest) returns (HelloResponse);
}
