/*
 * Copyright 2016 Google, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// 1. syntax, package, option
syntax = "proto3";

package com.example.grpc.error;

option java_multiple_files = true;

message EchoRequest {
    string message = 1;
}

message EchoResponse {
    string message = 1;
}

service ErrorService {
    rpc notImplemented(EchoRequest) returns (EchoResponse);
    rpc customUnwrapException(EchoRequest) returns (EchoResponse);
    rpc customException(EchoRequest) returns (EchoResponse);
    rpc uncaughtExceptions(EchoRequest) returns (EchoResponse);
    rpc deadlineExceeded(EchoRequest) returns (EchoResponse);
    rpc automaticallyWrappedException(EchoRequest) returns (EchoResponse);
}
