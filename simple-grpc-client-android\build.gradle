// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext.rx_java_verstion = '2.1.8'
    ext.rx_android = '2.0.1'

    repositories {
        google()
        jcenter()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:3.1.3'
        classpath 'com.google.protobuf:protobuf-gradle-plugin:0.8.5'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        jcenter()
        maven { url "https://plugins.gradle.org/m2/" }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
