<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>grpc-demos</artifactId>
        <groupId>com.example</groupId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.example.kubernetes</groupId>
    <artifactId>kubernetes-lb-example</artifactId>
    <packaging>pom</packaging>

    <properties>
        <docker.image.prefix>saturnism</docker.image.prefix>
    </properties>

    <modules>
        <module>echo-server</module>
        <module>echo-client-simple</module>
        <module>echo-client-lb-dns</module>
        <module>echo-client-lb-api</module>
    </modules>
</project>
